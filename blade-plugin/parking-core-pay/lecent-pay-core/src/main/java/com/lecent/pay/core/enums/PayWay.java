package com.lecent.pay.core.enums;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Getter
public enum PayWay {


	/**
	 * 现金
	 */
	CASH(1, "现金", PayChannel.CASH, null),

	SIMULATION(2, "模拟支付", null, null),

	WX_QR(5001, "微信二维码", PayChannel.WX, Sets.newHashSet(
		PayScene.PAY_JSAPI,
		PayScene.PAY_NATIVE,
		PayScene.PAY_MINI)),

	WX_PP(5005, "微信公众号", PayChannel.WX, Sets.newHashSet(PayScene.PAY_JSAPI)),

	WX_H5(5008, "微信-h5催缴", PayChannel.WX, Sets.newHashSet(PayScene.PAY_JSAPI)),

	WX_NO_TOUCH_PAY(5009, "微信-无感支付", PayChannel.WX, Sets.newHashSet(PayScene.PAY_BE)),

	BLX(5010, "比邻星平台", PayChannel.BLX, null),
	/**
	 * 5011.支付宝
	 */
	ALI(5011,"支付宝-无感支付", PayChannel.ALI, Sets.newHashSet(PayScene.PAY_BE)),

	ETC(100, "ETC支付", PayChannel.ETC, Sets.newHashSet(PayScene.PAY_ETC, PayScene.PAY_BE)),

	QTZL_TECH(1001, "黔通智联ETC", null, null),
	XL_TECH(1002, "山东高速ETC", null, null),
	/**
	 * 建行聚合支付
	 */
	CCB_POLYMERIZATION(200, "建行聚合支付", PayChannel.CCB,
		Sets.newHashSet(PayScene.PAY_JSAPI,
			PayScene.PAY_MICRO,
			PayScene.PAY_H5,
			PayScene.PAY_NATIVE,
			PayScene.PAY_MINI)),

	/**
	 * 建行无感支付
	 */
	CCB_NO_TOUCH_PAY(201, "建行无感支付", PayChannel.CCB, Sets.newHashSet(PayScene.PAY_BE)),

	/**
	 * 工行聚合支付
	 */
	ICBC_POLYMERIZATION(210, "工行聚合支付", PayChannel.ICBC_E,
		Sets.newHashSet(PayScene.PAY_JSAPI,
			PayScene.PAY_MICRO,
			PayScene.PAY_H5,
			PayScene.PAY_NATIVE,
			PayScene.PAY_MINI)),

	/**
	 * 工行无感支付
	 */
	ICBC_NO_TOUCH_PAY(211, "工行无感支付", PayChannel.ICBC_E, Sets.newHashSet(PayScene.PAY_BE)),

	WX_PDA(5009, "微信-pda执勤收费", PayChannel.WX, null),

	/**
	 * 优惠券支付
	 */
	COUPON(300, "优惠券", null, null),

	/**
	 * 积分支付
	 */
	POINTS(301, "积分", null, null),

	/**
	 * 储值卡支付
	 */
	STORED_VALUE_CARD(302, "储值卡", null, null),

	UNKNOWN(-1, "未知渠道", null, null),

	;

	/**
	 * 状态码
	 */
	private final int key;

	/**
	 * 支付名称
	 */
	private final String name;

	/**
	 * 支付渠道
	 */
	private final PayChannel payChannel;

	/**
	 * 支付场景
	 */
	private final Set<PayScene> payScenes;

	PayWay(int key, String name, PayChannel payChannel, Set<PayScene> payScenes) {
		this.key = key;
		this.name = name;
		this.payChannel = payChannel;
		this.payScenes = payScenes;
	}

	public static String getNameByKey(int key) {
		for (PayWay pw : PayWay.values()) {
			if (pw.getKey() == key) {
				return pw.getName();
			}
		}
		return "";
	}


	/**
	 * 获取支付状态码
	 *
	 * @param payChannelValue 支付方渠道
	 * @param payScene        支付场景
	 * @return code
	 */
	public static int getPayCode(int payChannelValue, PayScene payScene) {
		return getPayCode(PayChannel.payChannelValue(payChannelValue), payScene);
	}

	/**
	 * 获取支付状态码
	 *
	 * @param payChannel 支付方渠道
	 * @param payScene   支付场景
	 * @return code
	 */
	public static int getPayCode(PayChannel payChannel, PayScene payScene) {
		return resolve(payChannel, payScene).getKey();
	}

	/**
	 * 根据支付方渠道、支付场景
	 * 解析支付方式
	 *
	 * @param payChannel 支付方渠道
	 * @param payScene   支付场景
	 * @return PayWay 支付方式
	 */
	public static PayWay resolve(PayChannel payChannel, PayScene payScene) {
		for (PayWay payWay : PayWay.values()) {
			if (payWay.getPayChannel() == payChannel) {
				if (null != payWay.getPayScenes() && payWay.getPayScenes().contains(payScene)) {
					return payWay;
				}
			}
		}

		if (PayChannel.WX == payChannel) {
			return PayWay.WX_QR;
		}

		if (PayChannel.CCB == payChannel) {
			return PayWay.CCB_POLYMERIZATION;
		}

		if (PayChannel.ICBC_E == payChannel) {
			return PayWay.ICBC_POLYMERIZATION;
		}

		return PayWay.UNKNOWN;
	}

	public static PayWay resolve(Integer key) {
		return Stream.of(values())
			.filter(t -> t.key == key)
			.findFirst()
			.orElse(PayWay.UNKNOWN);
	}

	/**
	 * 获取支付渠道
	 *
	 * @param payWay 支付方式
	 * @return {@link PayChannel}
	 */
	public static PayChannel getPayChannel(Integer payWay) {
		PayChannel channel = null;
		for (PayWay value : values()) {
			if (value.key == payWay) {
				switch (value) {
					case WX_H5:
					case WX_NO_TOUCH_PAY:
					case WX_QR:
					case WX_PDA:
					case WX_PP:
						channel = PayChannel.WX;
						break;
					case ETC:
						channel = PayChannel.ETC;
						break;
					case ICBC_POLYMERIZATION:
					case ICBC_NO_TOUCH_PAY:
						channel = PayChannel.ICBC_E;
						break;
					case CCB_NO_TOUCH_PAY:
					case CCB_POLYMERIZATION:
						channel = PayChannel.CCB;
						break;
					default:
				}
			}
		}
		return channel;
	}

	public static List<Integer> getPayWays(PayChannel payChannel){
		List<Integer> payways =new ArrayList<>();
		for (PayWay value : values()) {
			if (value.payChannel != null && value.payChannel == payChannel) {
				payways.add(value.key);
			}
		}
		return payways;
	}

}
