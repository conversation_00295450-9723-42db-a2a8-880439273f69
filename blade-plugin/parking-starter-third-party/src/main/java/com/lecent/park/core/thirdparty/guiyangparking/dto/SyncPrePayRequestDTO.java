package com.lecent.park.core.thirdparty.guiyangparking.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 缴费通知请求DTO
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@ApiModel(description = "缴费通知请求")
public class SyncPrePayRequestDTO {

    /**
     * 车场编码
     */
    @ApiModelProperty(value = "车场编码")
    private String parkCode;

    /**
     * 三方车场编码
     */
    @ApiModelProperty(value = "三方车场编码")
    private String thirdParkCode;

    /**
     * 停车记录号
     */
    @ApiModelProperty(value = "停车记录号", required = true)
    private String orderId;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", required = true)
    private String plate;

    /**
     * 交易流水号
     */
    @ApiModelProperty(value = "交易流水号", required = true)
    private String tradeNo;

    /**
     * 应收金额（分）
     */
    @ApiModelProperty(value = "应收金额（分）", required = true)
    private String totalAmount;

    /**
     * 实付金额（分）
     */
    @ApiModelProperty(value = "实付金额（分）", required = true)
    private String paidAmount;

    /**
     * 平台优惠金额（分）
     */
    @ApiModelProperty(value = "平台优惠金额（分）", required = true)
    private String platformDiscountAmount;

    /**
     * 本地优惠金额（分）
     */
    @ApiModelProperty(value = "本地优惠金额（分）")
    private String localDiscountAmount;

    /**
     * 支付方式
     * 00：现金，01：微信，02：支付宝，03：银联，04：ETC，05：优惠券，06：积分 07: 储值卡
     */
    @ApiModelProperty(value = "支付方式", required = true, example = "00：现金，01：微信，02：支付宝，03：银联，04：ETC，05：优惠券，06：积分 07: 储值卡")
    private String payType;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间", required = true, example = "2025-03-01 20:00:00")
    private String paidTime;

    /**
     * 支付来源
     * 00：出口岗亭，01：自助缴费机，02：移动岗亭，03：H5扫码，04：公众号，05：平台无感，06：第三方平台，07：第三方无感
     */
    @ApiModelProperty(value = "支付来源", required = true, example = "00：出口岗亭，01：自助缴费机，02：移动岗亭，03：H5扫码，04：公众号，05：平台无感，06：第三方平台，07：第三方无感")
    private String paySource;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间", example = "2025-03-01 20:00:00")
    private String upString;

    /**
     * 支付人
     * 00：车主本人支付，01：访客,业主代缴支付
     */
    @ApiModelProperty(value = "支付人", example = "00：车主本人支付，01：访客,业主代缴支付")
    private String payer;
} 