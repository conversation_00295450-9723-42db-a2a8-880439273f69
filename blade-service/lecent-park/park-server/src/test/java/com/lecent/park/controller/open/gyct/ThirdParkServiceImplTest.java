package com.lecent.park.controller.open.gyct;

import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayResponseDTO;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.service.IChannelTodoService;
import com.lecent.park.service.ITempParkingChargeRuleService;
import com.lecent.park.service.ITempParkingOrderService;
import com.lecent.pay.core.enums.PayWay;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * ThirdParkServiceImpl测试类
 * <AUTHOR>
 * @date 2024-03-21
 */
@ExtendWith(MockitoExtension.class)
class ThirdParkServiceImplTest {

    @Mock
    private IChannelTodoService channelTodoService;

    @Mock
    private ITempParkingChargeRuleService tempParkingChargeRuleService;

    @Mock
    private ITempParkingOrderService tempParkingOrderService;

    @InjectMocks
    private ThirdParkServiceImpl thirdParkService;

    private SyncPrePayRequestDTO request;
    private ChannelTodo channelTodo;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        request = new SyncPrePayRequestDTO();
        request.setOrderId("123456");
        request.setPlate("粤A12345");
        request.setTradeNo("TXN20240321001");
        request.setTotalAmount("1000"); // 10元，单位分
        request.setPaidAmount("1000");
        request.setPlatformDiscountAmount("0");
        request.setPayType("01"); // 微信支付
        request.setPaidTime("2024-03-21 10:30:00");
        request.setPaySource("03"); // H5扫码

        // 准备待办记录
        channelTodo = new ChannelTodo();
        channelTodo.setId(123456L);
        channelTodo.setPlate("粤A12345");
        channelTodo.setParklotId(1L);
        channelTodo.setChannelId(1L);
        channelTodo.setParkingId(1L);
        channelTodo.setVehicleId(1L);
        channelTodo.setReceiveAmount(new BigDecimal("10.00"));
        channelTodo.setPaidAmount(BigDecimal.ZERO);
        channelTodo.setDiscountAmount(BigDecimal.ZERO);
        channelTodo.setMerchantAmount(new BigDecimal("10.00"));
        channelTodo.setEnterTime(new Date());
    }

    @Test
    void testSyncPrePay_Success() {
        // 模拟依赖服务的行为
        when(channelTodoService.getById(anyLong())).thenReturn(channelTodo);
        when(tempParkingOrderService.save(any(TempParkingOrder.class))).thenReturn(true);

        // 执行测试
        SyncPrePayResponseDTO response = thirdParkService.syncPrePay(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("200", response.getCode());
        assertEquals("成功", response.getMsg());

        // 验证方法调用
        verify(channelTodoService, times(1)).getById(123456L);
        verify(tempParkingOrderService, times(1)).save(any(TempParkingOrder.class));
    }

    @Test
    void testSyncPrePay_TodoNotFound() {
        // 模拟待办记录不存在
        when(channelTodoService.getById(anyLong())).thenReturn(null);

        // 执行测试
        SyncPrePayResponseDTO response = thirdParkService.syncPrePay(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("1034", response.getCode());
        assertEquals("待办记录不存在", response.getMsg());

        // 验证方法调用
        verify(channelTodoService, times(1)).getById(123456L);
        verify(tempParkingOrderService, never()).save(any(TempParkingOrder.class));
    }

    @Test
    void testSyncPrePay_InvalidRequest() {
        // 准备无效请求
        SyncPrePayRequestDTO invalidRequest = new SyncPrePayRequestDTO();
        invalidRequest.setOrderId(""); // 空的订单ID

        // 执行测试
        SyncPrePayResponseDTO response = thirdParkService.syncPrePay(invalidRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals("1034", response.getCode());
        assertTrue(response.getMsg().contains("不能为空"));

        // 验证方法调用
        verify(channelTodoService, never()).getById(anyLong());
        verify(tempParkingOrderService, never()).save(any(TempParkingOrder.class));
    }

    @Test
    void testSyncPrePay_UnknownPayType() {
        // 设置未知支付方式
        request.setPayType("99");

        // 模拟依赖服务的行为
        when(channelTodoService.getById(anyLong())).thenReturn(channelTodo);
        when(tempParkingOrderService.save(any(TempParkingOrder.class))).thenReturn(true);

        // 执行测试
        SyncPrePayResponseDTO response = thirdParkService.syncPrePay(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("200", response.getCode());
        assertEquals("成功", response.getMsg());

        // 验证方法调用
        verify(channelTodoService, times(1)).getById(123456L);
        verify(tempParkingOrderService, times(1)).save(any(TempParkingOrder.class));
    }

    @Test
    void testThirdPartyPayType_Mapping() {
        // 测试支付方式映射
        assertEquals(PayWay.CASH.getKey(), ThirdPartyPayType.getPayWayKeyByCode("00"));
        assertEquals(PayWay.WX_QR.getKey(), ThirdPartyPayType.getPayWayKeyByCode("01"));
        assertEquals(PayWay.ALI.getKey(), ThirdPartyPayType.getPayWayKeyByCode("02"));
        assertEquals(PayWay.CCB_POLYMERIZATION.getKey(), ThirdPartyPayType.getPayWayKeyByCode("03"));
        assertEquals(PayWay.ETC.getKey(), ThirdPartyPayType.getPayWayKeyByCode("04"));
        assertEquals(PayWay.COUPON.getKey(), ThirdPartyPayType.getPayWayKeyByCode("05"));
        assertEquals(PayWay.POINTS.getKey(), ThirdPartyPayType.getPayWayKeyByCode("06"));
        assertEquals(PayWay.STORED_VALUE_CARD.getKey(), ThirdPartyPayType.getPayWayKeyByCode("07"));
        assertEquals(PayWay.UNKNOWN.getKey(), ThirdPartyPayType.getPayWayKeyByCode("99"));
    }

    @Test
    void testSyncPrePay_Exception() {
        // 模拟服务异常
        when(channelTodoService.getById(anyLong())).thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        SyncPrePayResponseDTO response = thirdParkService.syncPrePay(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("1001", response.getCode());
        assertEquals("内部服务器异常", response.getMsg());
    }
}
