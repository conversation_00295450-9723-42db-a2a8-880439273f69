# 一、第三方车场查询停车费用（下行）

## 1. 接口描述
从平台向第三方车场查询车辆停车费用。

## 2. 接口地址
请求地址：`https://{{url}}/thirdPark/queryOrderFee`  
注：此处`url`为第三方车场地址  
请求方式：`POST`

## 3. 请求头部
| 参数名       | 是否必须 | 类型   | 说明 |
| ------------ | -------- | ------ | ---- |
| Content-Type | 是       | String | json |

## 4. 请求参数
| 入参名称       | 类型                     | 长度 | 是否必须 | 说明                                               |
| -------------- | ------------------------ | ---- | -------- | -------------------------------------------------- |
| parkCode       | String                   | 32   | 否       | 平台车场编码                                       |
| thirdParkCode  | String                   | 32   | 否       | 第三方车场编码                                     |
| plate          | String                   | 16   | 否       | 车牌和出口通道ID不能同时为空，同时存在以通道ID为主 |
| channelId      | String                   | 32   | 否       | 出口通道ID                                         |
| thirdChannelId | String                   | 32   | 否       | 第三方通道ID                                       |
| localOrderId   | String                   | 32   | 否       | 本地停车记录号                                     |
| plateColor     | String                   | 2    | 否       | 车牌颜色                                           |
| coupons        | List<Map<String,String>> | -    | 否       | 优惠券信息                                         |

### coupons 参数
| 入参名称        | 类型   | 长度 | 是否必须 | 说明                                                         |
| --------------- | ------ | ---- | -------- | ------------------------------------------------------------ |
| couponId        | String | 32   | 是       | 优惠券ID                                                     |
| couponType      | String | 16   | 是       | 优惠券类型：01 时长优惠，02 金额优惠，03 全免优惠，04 折扣   |
| discountValue   | String | 16   | 是       | 根据类型表示不同的值：01 时长券，单位为分钟；02 表示金额券，单位为分；03 表示全免此值为 0；04 折扣券存放%前的折扣整数（即计算时，折扣比例先除100）；05 默认为0取时段开始时间与结束时间 |
| usableStartTime | String | 20   | 否       | 格式为`yyyy-MM-dd hh:mm:ss`，开始时间                        |
| usableEndTime   | String | 20   | 否       | 格式为`yyyy-MM-dd hh:mm:ss`，结束时间                        |

## 5. 返回参数
| 参数名 | 是否必须 | 类型   | 说明           |
| ------ | -------- | ------ | -------------- |
| code   | 是       | String | 业务返回状态码 |
| msg    | 是       | String | 业务返回信息   |
| data   | 是       | Object | 业务响应数据   |

### data 参数
| 参数名         | 是否必须 | 类型                     | 说明                                                 |
| -------------- | -------- | ------------------------ | ---------------------------------------------------- |
| parkCode       | 是       | String                   | 车场ID，入参里的parkCode                             |
| orderId        | 是       | String                   | 本地停车记录号                                       |
| totalAmount    | 是       | String                   | 总金额（从车辆入场至当前时间计费的总费用，单位：分） |
| paidAmount     | 是       | String                   | 已缴金额（单位：分）                                 |
| discountAmount | 是       | String                   | 优惠金额（单位：分）                                 |
| unpayAmount    | 是       | String                   | 待付金额（单位：分）                                 |
| parkingTime    | 是       | String                   | 停车总时长（单位秒）                                 |
| paidTime       | 是       | String                   | 最后支付时间（格式为`yyyy-MM-dd hh:mm:ss`）          |
| enterTime      | 是       | String                   | 入场时间（格式为`yyyy-MM-dd hh:mm:ss`）              |
| plate          | 是       | String                   | 车牌号                                               |
| parkName       | 是       | String                   | 车场名称                                             |
| coupons        | 否       | List<Map<String,String>> | 优惠券信息                                           |
| freeTime       | 是       | String                   | 免费出场时间，单位：分钟                             |

### coupons 参数
| 参数名            | 是否必须 | 类型   | 说明                 |
| ----------------- | -------- | ------ | -------------------- |
| couponId          | 是       | String | 优惠券ID             |
| currDiscountPrice | 是       | String | 实际优惠金额，单位分 |
| currDiscountTime  | 否       | String | 实际优惠时间，单位分 |

## 6. 业务返回编码
| 编码（ret_code） | 含义（ret_msg） |
| ---------------- | --------------- |
| 200              | 成功            |
| 1034             | 车牌号无效      |
| 1001             | 内部服务器异常  |
| 6666             | 超时支付        |
| 7777             | 未出场无需支付  |
| 8888             | 等待支付        |
| 9999             | 无需缴费        |



# 二、第三方车场缴费通知接口（下行）

## 1. 接口描述
从平台支付完成后，通知第三方车场支付结果。

## 2. 接口地址
请求地址：`https://{{url}}/thirdPark/syncPrePay`  
注：此处`url`为第三方车场地址  
请求方式：`POST`

## 3. 请求头部
| 参数名       | 是否必须 | 类型   | 说明 |
| ------------ | -------- | ------ | ---- |
| Content-Type | 是       | String | json |

## 4. 请求参数
| 入参名称               | 类型   | 长度 | 是否必须 | 说明                                                         |
| ---------------------- | ------ | ---- | -------- | ------------------------------------------------------------ |
| parkCode               | String | 32   | 否       | 平台车场编码                                                 |
| thirdParkCode          | String | 32   | 否       | 第三方车场编码                                               |
| orderId                | String | 32   | 是       | 本地上报的停车记录号                                         |
| plate                  | String | 16   | 是       | 车牌号                                                       |
| tradeNo                | String | 32   | 是       | 平台单次支付的交易流水号                                     |
| totalAmount            | String | 10   | 是       | 应收金额，单位：分                                           |
| paidAmount             | String | 10   | 是       | 实付金额，单位：分                                           |
| platformDiscountAmount | String | 16   | 是       | 平台优惠金额，单位：分                                       |
| localDiscountAmount    | String | 16   | 否       | 本地优惠金额，单位：分                                       |
| payType                | String | 10   | 是       | 支付方式：00 现金，01 微信，02 支付宝，03 银联，04 ETC，05 优惠券，06 积分，07 储值卡 |
| paidTime               | String | 20   | 是       | 支付完成时间，格式为`yyyy-MM-dd hh:mm:ss`                    |
| paySource              | String | 2    | 是       | 支付来源：00 出口岗亭，01 自助缴费机，02 移动岗亭，03 H5扫码，04 公众号，05 平台无感，06 第三方平台，07 第三方无感 |
| upString               | String | 20   | 否       | 下发时间，格式为`yyyy-MM-dd hh:mm:ss`                        |
| payer                  | String | 2    | 否       | 支付人：00 车主本人支付，01 访客，业主代缴支付，不传默认是车主支付 |

## 5. 返回参数
| 参数名 | 是否必须 | 类型   | 说明           |
| ------ | -------- | ------ | -------------- |
| code   | 是       | String | 业务返回状态码 |
| msg    | 是       | String | 业务返回信息   |
| data   | 是       | Object | 业务响应数据   |

