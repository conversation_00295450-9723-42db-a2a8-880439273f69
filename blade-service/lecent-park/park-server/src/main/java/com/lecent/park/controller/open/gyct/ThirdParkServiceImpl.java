package com.lecent.park.controller.open.gyct;

import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.core.thirdparty.guiyangparking.dto.QueryOrderFeeRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.QueryOrderFeeResponseDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayResponseDTO;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IChannelTodoService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.ITempParkingChargeRuleService;
import com.lecent.park.vo.ChannelTodoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;

/**
 * 第三方车场服务实现类
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdParkServiceImpl implements ThirdParkService {

    private final IChannelTodoService channelTodoService;
    private final ITempParkingChargeRuleService tempParkingChargeRuleService;

    @Override
    public QueryOrderFeeResponseDTO queryOrderFee(QueryOrderFeeRequestDTO request) {
        log.info("查询停车费用请求参数：{}", request);
        QueryOrderFeeResponseDTO response = new QueryOrderFeeResponseDTO();
        try {
            // 1. 参数校验
            validateQueryOrderFeeRequest(request);

            // 2. 查询最后一条待办记录
            ChannelTodoVO channelTodo = queryLastTodoRecord(request);
            if (channelTodo == null) {
                response.setCode("1034");
                response.setMsg("车牌号无效");
                return response;
            }

            // 3. 查询车场信息
            Parklot parklot = ParkLotCaches.getParkLot(channelTodo.getParklotId());
            if (parklot == null) {
                response.setCode("1001");
                response.setMsg("车场信息不存在");
                return response;
            }

            // 4. 组装响应数据
            QueryOrderFeeResponseDTO.OrderFeeDataDTO data = buildOrderFeeData(request, channelTodo, parklot);
            response.setData(data);
            response.setCode("200");
            response.setMsg("成功");
        } catch (Exception e) {
            log.error("查询停车费用异常", e);
            response.setCode("1001");
            response.setMsg("内部服务器异常");
        }
        return response;
    }

    @Override
    public SyncPrePayResponseDTO syncPrePay(SyncPrePayRequestDTO request) {
        log.info("缴费通知请求参数：{}", request);
        SyncPrePayResponseDTO response = new SyncPrePayResponseDTO();
        try {
            // 1. 参数校验
            validateSyncPrePayRequest(request);

            // 2. 更新支付状态
            // 3. 记录支付日志
            // 4. 组装响应数据
            // TODO: 实现缴费通知的具体业务逻辑

            response.setCode("200");
            response.setMsg("成功");
        } catch (Exception e) {
            log.error("缴费通知异常", e);
            response.setCode("1001");
            response.setMsg("内部服务器异常");
        }
        return response;
    }

    /**
     * 查询最后一条待办记录
     * @param request 请求参数
     * @return 待办记录
     */
    private ChannelTodoVO queryLastTodoRecord(QueryOrderFeeRequestDTO request) {
        try {
            // 通道id和车牌都存在时，通道id优先
            if (Func.isNotBlank(request.getThirdChannelId())) {
                // 根据第三方通道ID查询通道信息
                // 注意：由于实体类中没有第三方编码字段，这里假设thirdChannelId就是系统内部的channelId
                Long channelId = Long.valueOf(request.getThirdChannelId());
                return channelTodoService.latestInfoByChannelId(channelId);
            } else if (Func.isNotBlank(request.getPlate()) && Func.isNotBlank(request.getThirdParkCode())) {
                // 根据车场编码和车牌查询
                // 注意：由于实体类中没有第三方编码字段，这里假设thirdParkCode就是系统内部的parklotId
                Long parklotId = Long.valueOf(request.getThirdParkCode());
                return channelTodoService.selectLatestOneByPlate(parklotId, request.getPlate());
            }
        } catch (NumberFormatException e) {
            log.error("参数格式错误：{}", e.getMessage());
        } catch (Exception e) {
            log.error("查询待办记录异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 组装订单费用数据
     * @param request 请求参数
     * @param channelTodo 待办记录
     * @param parklot 车场信息
     * @return 订单费用数据
     */
    private QueryOrderFeeResponseDTO.OrderFeeDataDTO buildOrderFeeData(QueryOrderFeeRequestDTO request,
                                                                        ChannelTodoVO channelTodo,
                                                                        Parklot parklot) {
        QueryOrderFeeResponseDTO.OrderFeeDataDTO data = new QueryOrderFeeResponseDTO.OrderFeeDataDTO();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 基本信息
        data.setParkCode(request.getThirdParkCode());
        data.setOrderId(channelTodo.getId().toString());
        data.setPlate(channelTodo.getPlate());
        data.setParkName(parklot.getName());

        // 时间信息
        if (channelTodo.getEnterTime() != null) {
            data.setEnterTime(dateFormat.format(channelTodo.getEnterTime()));
            // 计算停车时长（秒）
            long parkingTimeSeconds = (System.currentTimeMillis() - channelTodo.getEnterTime().getTime()) / 1000;
            data.setParkingTime(String.valueOf(parkingTimeSeconds));
        }

        // 支付时间
        if (channelTodo.getPaymentTime() != null) {
            data.setPaidTime(dateFormat.format(channelTodo.getPaymentTime()));
        } else {
            data.setPaidTime("");
        }

        // 金额信息（转换为分）
        BigDecimal totalAmount = channelTodo.getTotalAmount() != null ? channelTodo.getTotalAmount() : BigDecimal.ZERO;
        BigDecimal paidAmount = channelTodo.getPaidAmount() != null ? channelTodo.getPaidAmount() : BigDecimal.ZERO;
        BigDecimal discountAmount = channelTodo.getDiscountAmount() != null ? channelTodo.getDiscountAmount() : BigDecimal.ZERO;

        data.setTotalAmount(totalAmount.multiply(new BigDecimal("100")).toBigInteger().toString());
        data.setPaidAmount(paidAmount.multiply(new BigDecimal("100")).toBigInteger().toString());
        data.setDiscountAmount(discountAmount.multiply(new BigDecimal("100")).toBigInteger().toString());

        // 计算待付金额
        BigDecimal unpayAmount = totalAmount.subtract(paidAmount).subtract(discountAmount);
        data.setUnpayAmount(unpayAmount.multiply(new BigDecimal("100")).toBigInteger().toString());

        // 获取免费出场时间
        Integer freeTime = tempParkingChargeRuleService.getPayLeaveTimeByParkLotId(parklot);
        data.setFreeTime(freeTime.toString());

        return data;
    }

    /**
     * 校验查询停车费用请求参数
     * @param request 请求参数
     */
    private void validateQueryOrderFeeRequest(QueryOrderFeeRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (Func.isBlank(request.getPlate()) && Func.isBlank(request.getThirdChannelId())) {
            throw new IllegalArgumentException("车牌号和出口通道ID不能同时为空");
        }
        if (Func.isBlank(request.getThirdParkCode())) {
            throw new IllegalArgumentException("第三方车场编码不能为空");
        }
    }

    /**
     * 校验缴费通知请求参数
     * @param request 请求参数
     */
    private void validateSyncPrePayRequest(SyncPrePayRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (Func.isBlank(request.getOrderId()) || Func.isBlank(request.getPlate()) || Func.isBlank(request.getTradeNo())) {
            throw new IllegalArgumentException("停车记录号、车牌号、交易流水号不能为空");
        }
    }
} 