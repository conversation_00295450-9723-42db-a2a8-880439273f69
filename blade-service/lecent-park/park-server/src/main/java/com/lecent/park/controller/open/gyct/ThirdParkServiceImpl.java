package com.lecent.park.controller.open.gyct;

import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.core.thirdparty.guiyangparking.dto.QueryOrderFeeRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.QueryOrderFeeResponseDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.dto.SyncPrePayResponseDTO;
import com.lecent.park.entity.ChannelTodo;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.park.service.IChannelTodoService;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.ITempParkingChargeRuleService;
import com.lecent.park.service.ITempParkingOrderService;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.pay.core.enums.PayWay;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 第三方支付方式枚举
 * 根据接口文档定义的支付方式与系统PayWay枚举对应
 */
@Getter
enum ThirdPartyPayType {
    CASH("00", "现金", PayWay.CASH),
    WECHAT("01", "微信", PayWay.WX_QR),
    ALIPAY("02", "支付宝", PayWay.ALI),
    UNION_PAY("03", "银联", PayWay.CCB_POLYMERIZATION),
    ETC("04", "ETC", PayWay.ETC),
    COUPON("05", "优惠券", null), // 系统中没有对应的优惠券支付方式，需要添加
    POINTS("06", "积分", null), // 系统中没有对应的积分支付方式，需要添加
    STORED_VALUE_CARD("07", "储值卡", null); // 系统中没有对应的储值卡支付方式，需要添加

    private final String code;
    private final String name;
    private final PayWay payWay;

    ThirdPartyPayType(String code, String name, PayWay payWay) {
        this.code = code;
        this.name = name;
        this.payWay = payWay;
    }

    /**
     * 根据第三方支付方式代码获取系统支付方式
     * @param code 第三方支付方式代码
     * @return 系统支付方式
     */
    public static PayWay getPayWayByCode(String code) {
        for (ThirdPartyPayType type : values()) {
            if (type.getCode().equals(code)) {
                return type.getPayWay();
            }
        }
        return PayWay.UNKNOWN;
    }

    /**
     * 根据第三方支付方式代码获取支付方式key
     * @param code 第三方支付方式代码
     * @return 支付方式key
     */
    public static Integer getPayWayKeyByCode(String code) {
        PayWay payWay = getPayWayByCode(code);
        return payWay != null ? payWay.getKey() : PayWay.UNKNOWN.getKey();
    }
}

/**
 * 第三方车场服务实现类
 * <AUTHOR>
 * @date 2024-03-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdParkServiceImpl implements ThirdParkService {

    private final IChannelTodoService channelTodoService;
    private final ITempParkingChargeRuleService tempParkingChargeRuleService;
    private final ITempParkingOrderService tempParkingOrderService;

    @Override
    public QueryOrderFeeResponseDTO queryOrderFee(QueryOrderFeeRequestDTO request) {
        log.info("查询停车费用请求参数：{}", request);
        QueryOrderFeeResponseDTO response = new QueryOrderFeeResponseDTO();
        try {
            // 1. 参数校验
            validateQueryOrderFeeRequest(request);

            // 2. 查询最后一条待办记录
            ChannelTodoVO channelTodo = queryLastTodoRecord(request);
            if (channelTodo == null) {
                response.setCode("1034");
                response.setMsg("车牌号无效");
                return response;
            }

            // 3. 查询车场信息
            Parklot parklot = ParkLotCaches.getParkLot(channelTodo.getParklotId());
            if (parklot == null) {
                response.setCode("1001");
                response.setMsg("车场信息不存在");
                return response;
            }

            // 4. 组装响应数据
            QueryOrderFeeResponseDTO.OrderFeeDataDTO data = buildOrderFeeData(request, channelTodo, parklot);
            response.setData(data);
            response.setCode("200");
            response.setMsg("成功");
        } catch (Exception e) {
            log.error("查询停车费用异常", e);
            response.setCode("1001");
            response.setMsg("内部服务器异常");
        }
        return response;
    }

    @Override
    public SyncPrePayResponseDTO syncPrePay(SyncPrePayRequestDTO request) {
        log.info("缴费通知请求参数：{}", request);
        SyncPrePayResponseDTO response = new SyncPrePayResponseDTO();
        try {
            // 1. 参数校验
            validateSyncPrePayRequest(request);

            // 2. 通过待办ID查询待办记录
            ChannelTodo channelTodo = queryChannelTodoById(request.getOrderId());
            if (channelTodo == null) {
                response.setCode("1034");
                response.setMsg("待办记录不存在");
                return response;
            }

            // 3. 获取支付方式
            Integer payType = ThirdPartyPayType.getPayWayKeyByCode(request.getPayType());
            if (payType == null || payType.equals(PayWay.UNKNOWN.getKey())) {
                log.warn("不支持的支付方式：{}", request.getPayType());
                payType = PayWay.UNKNOWN.getKey();
            }

            // 4. 创建临停订单
            TempParkingOrder tempOrder = createTempParkingOrder(request, channelTodo, payType);

            // 5. 保存临停订单
            tempParkingOrderService.save(tempOrder);

            log.info("成功创建临停订单，订单ID：{}，交易流水号：{}", tempOrder.getId(), tempOrder.getTradeNo());

            response.setCode("200");
            response.setMsg("成功");
        } catch (IllegalArgumentException e) {
            log.error("参数校验失败：{}", e.getMessage());
            response.setCode("1034");
            response.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("缴费通知异常", e);
            response.setCode("1001");
            response.setMsg("内部服务器异常");
        }
        return response;
    }

    /**
     * 查询最后一条待办记录
     * @param request 请求参数
     * @return 待办记录
     */
    private ChannelTodoVO queryLastTodoRecord(QueryOrderFeeRequestDTO request) {
        try {
            // 通道id和车牌都存在时，通道id优先
            if (Func.isNotBlank(request.getThirdChannelId())) {
                // 根据第三方通道ID查询通道信息
                // 注意：由于实体类中没有第三方编码字段，这里假设thirdChannelId就是系统内部的channelId
                Long channelId = Long.valueOf(request.getThirdChannelId());
                return channelTodoService.latestInfoByChannelId(channelId);
            } else if (Func.isNotBlank(request.getPlate()) && Func.isNotBlank(request.getThirdParkCode())) {
                // 根据车场编码和车牌查询
                // 注意：由于实体类中没有第三方编码字段，这里假设thirdParkCode就是系统内部的parklotId
                Long parklotId = Long.valueOf(request.getThirdParkCode());
                return channelTodoService.selectLatestOneByPlate(parklotId, request.getPlate());
            }
        } catch (NumberFormatException e) {
            log.error("参数格式错误：{}", e.getMessage());
        } catch (Exception e) {
            log.error("查询待办记录异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 组装订单费用数据
     * @param request 请求参数
     * @param channelTodo 待办记录
     * @param parklot 车场信息
     * @return 订单费用数据
     */
    private QueryOrderFeeResponseDTO.OrderFeeDataDTO buildOrderFeeData(QueryOrderFeeRequestDTO request,
                                                                        ChannelTodoVO channelTodo,
                                                                        Parklot parklot) {
        QueryOrderFeeResponseDTO.OrderFeeDataDTO data = new QueryOrderFeeResponseDTO.OrderFeeDataDTO();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 基本信息
        data.setParkCode(request.getThirdParkCode());
        data.setOrderId(channelTodo.getId().toString());
        data.setPlate(channelTodo.getPlate());
        data.setParkName(parklot.getName());

        // 时间信息
        if (channelTodo.getEnterTime() != null) {
            data.setEnterTime(dateFormat.format(channelTodo.getEnterTime()));
            // 计算停车时长（秒）
            long parkingTimeSeconds = (System.currentTimeMillis() - channelTodo.getEnterTime().getTime()) / 1000;
            data.setParkingTime(String.valueOf(parkingTimeSeconds));
        }

        // 支付时间
        if (channelTodo.getPaymentTime() != null) {
            data.setPaidTime(dateFormat.format(channelTodo.getPaymentTime()));
        } else {
            data.setPaidTime("");
        }

        // 金额信息（转换为分）
        BigDecimal totalAmount = channelTodo.getTotalAmount() != null ? channelTodo.getTotalAmount() : BigDecimal.ZERO;
        BigDecimal paidAmount = channelTodo.getPaidAmount() != null ? channelTodo.getPaidAmount() : BigDecimal.ZERO;
        BigDecimal discountAmount = channelTodo.getDiscountAmount() != null ? channelTodo.getDiscountAmount() : BigDecimal.ZERO;

        data.setTotalAmount(totalAmount.multiply(new BigDecimal("100")).toBigInteger().toString());
        data.setPaidAmount(paidAmount.multiply(new BigDecimal("100")).toBigInteger().toString());
        data.setDiscountAmount(discountAmount.multiply(new BigDecimal("100")).toBigInteger().toString());

        // 计算待付金额
        BigDecimal unpayAmount = totalAmount.subtract(paidAmount).subtract(discountAmount);
        data.setUnpayAmount(unpayAmount.multiply(new BigDecimal("100")).toBigInteger().toString());

        // 获取免费出场时间
        Integer freeTime = tempParkingChargeRuleService.getPayLeaveTimeByParkLotId(parklot);
        data.setFreeTime(freeTime.toString());

        return data;
    }

    /**
     * 校验查询停车费用请求参数
     * @param request 请求参数
     */
    private void validateQueryOrderFeeRequest(QueryOrderFeeRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (Func.isBlank(request.getPlate()) && Func.isBlank(request.getThirdChannelId())) {
            throw new IllegalArgumentException("车牌号和出口通道ID不能同时为空");
        }
        if (Func.isBlank(request.getThirdParkCode())) {
            throw new IllegalArgumentException("第三方车场编码不能为空");
        }
    }

    /**
     * 通过待办ID查询待办记录
     * @param orderId 待办ID
     * @return 待办记录
     */
    private ChannelTodo queryChannelTodoById(String orderId) {
        try {
            Long todoId = Long.valueOf(orderId);
            return channelTodoService.getById(todoId);
        } catch (NumberFormatException e) {
            log.error("待办ID格式错误：{}", orderId);
            return null;
        } catch (Exception e) {
            log.error("查询待办记录异常：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 创建临停订单
     * @param request 缴费通知请求
     * @param channelTodo 待办记录
     * @param payType 支付方式
     * @return 临停订单
     */
    private TempParkingOrder createTempParkingOrder(SyncPrePayRequestDTO request, ChannelTodo channelTodo, Integer payType) {
        TempParkingOrder tempOrder = TempParkingOrder.create(channelTodo);

        // 设置交易流水号
        tempOrder.setTradeNo(request.getTradeNo());

        // 设置支付方式
        tempOrder.setPayType(payType);

        // 设置支付状态为成功
        tempOrder.setPayStatus(1);

        // 设置支付时间
        if (Func.isNotBlank(request.getPaidTime())) {
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date payTime = dateFormat.parse(request.getPaidTime());
                tempOrder.setPayTime(payTime);
            } catch (Exception e) {
                log.warn("支付时间格式错误：{}，使用当前时间", request.getPaidTime());
                tempOrder.setPayTime(new Date());
            }
        } else {
            tempOrder.setPayTime(new Date());
        }

        // 设置金额信息（从分转换为元）
        if (Func.isNotBlank(request.getTotalAmount())) {
            BigDecimal totalAmount = new BigDecimal(request.getTotalAmount()).divide(new BigDecimal("100"));
            tempOrder.setTotalAmount(totalAmount);
        }

        if (Func.isNotBlank(request.getPaidAmount())) {
            BigDecimal paidAmount = new BigDecimal(request.getPaidAmount()).divide(new BigDecimal("100"));
            tempOrder.setPaidAmount(paidAmount);
        }

        if (Func.isNotBlank(request.getPlatformDiscountAmount())) {
            BigDecimal platformDiscountAmount = new BigDecimal(request.getPlatformDiscountAmount()).divide(new BigDecimal("100"));
            tempOrder.setDiscountAmount(platformDiscountAmount);
        }

        // 设置第三方支付订单号
        tempOrder.setThirdTradeNo(request.getTradeNo());

        // 设置备注信息
        String remark = String.format("第三方支付-%s-%s",
            ThirdPartyPayType.getPayWayByCode(request.getPayType()) != null ?
                ThirdPartyPayType.getPayWayByCode(request.getPayType()).getName() : "未知支付方式",
            request.getPaySource());
        tempOrder.setRemark(remark);

        return tempOrder;
    }

    /**
     * 校验缴费通知请求参数
     * @param request 请求参数
     */
    private void validateSyncPrePayRequest(SyncPrePayRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (Func.isBlank(request.getOrderId()) || Func.isBlank(request.getPlate()) || Func.isBlank(request.getTradeNo())) {
            throw new IllegalArgumentException("停车记录号、车牌号、交易流水号不能为空");
        }
        if (Func.isBlank(request.getPayType())) {
            throw new IllegalArgumentException("支付方式不能为空");
        }
        if (Func.isBlank(request.getTotalAmount()) || Func.isBlank(request.getPaidAmount())) {
            throw new IllegalArgumentException("应收金额、实付金额不能为空");
        }
    }
}